'use strict';

function _expect() {
  const data = _interopRequireDefault(require('expect'));

  _expect = function () {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}

/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
throw new Error(
  'Do not import `@jest/globals` outside of the Jest test environment'
);
