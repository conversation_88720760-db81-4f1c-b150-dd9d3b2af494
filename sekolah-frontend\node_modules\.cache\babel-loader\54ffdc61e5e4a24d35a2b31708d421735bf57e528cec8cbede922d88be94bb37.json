{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\sekolah\\\\sekolah-frontend\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navigation = [{\n  name: '<PERSON><PERSON><PERSON>',\n  href: '/'\n}, {\n  name: 'Profil',\n  href: '/profil'\n}, {\n  name: '<PERSON><PERSON>',\n  href: '/berita'\n}, {\n  name: '<PERSON><PERSON>',\n  href: '/galeri'\n}, {\n  name: '<PERSON>',\n  href: '/guru'\n}, {\n  name: '<PERSON><PERSON><PERSON>',\n  href: '/kontak'\n}];\nconst Header = () => {\n  _s();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-lg sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n      \"aria-label\": \"Top\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex w-full items-center justify-between border-b border-primary-500 py-6 lg:border-none\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-10 w-10 bg-primary-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-xl\",\n                children: \"S\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"SMA Negeri 1 Jakarta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Unggul dalam Prestasi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-10 hidden space-x-8 lg:block\",\n          children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            className: `text-base font-medium transition-colors duration-200 ${isActive(item.href) ? 'text-primary-600 border-b-2 border-primary-600 pb-1' : 'text-gray-700 hover:text-primary-600'}`,\n            children: item.name\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n            onClick: () => setMobileMenuOpen(!mobileMenuOpen),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sr-only\",\n              children: \"Open main menu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), mobileMenuOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"block h-6 w-6\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n              className: \"block h-6 w-6\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), mobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1 px-2 pb-3 pt-2\",\n          children: navigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            className: `block rounded-md px-3 py-2 text-base font-medium transition-colors duration-200 ${isActive(item.href) ? 'bg-primary-100 text-primary-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n            onClick: () => setMobileMenuOpen(false),\n            children: item.name\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"Bs2oUFLDOi+VE0tcASeCsSYgpZE=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "Bars3Icon", "XMarkIcon", "jsxDEV", "_jsxDEV", "navigation", "name", "href", "Header", "_s", "mobileMenuOpen", "setMobileMenuOpen", "location", "isActive", "path", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/sekolah/sekolah-frontend/src/components/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: '<PERSON><PERSON><PERSON>', href: '/' },\n  { name: 'Profil', href: '/profil' },\n  { name: '<PERSON><PERSON>', href: '/berita' },\n  { name: '<PERSON><PERSON>', href: '/galeri' },\n  { name: '<PERSON>', href: '/guru' },\n  { name: '<PERSON><PERSON><PERSON>', href: '/kontak' },\n];\n\nconst Header: React.FC = () => {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const location = useLocation();\n\n  const isActive = (path: string) => {\n    return location.pathname === path;\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex w-full items-center justify-between border-b border-primary-500 py-6 lg:border-none\">\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"h-10 w-10 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">S</span>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">SMA Negeri 1 Jakarta</h1>\n                <p className=\"text-sm text-gray-600\">Unggul dalam Prestasi</p>\n              </div>\n            </Link>\n          </div>\n          \n          {/* Desktop Navigation */}\n          <div className=\"ml-10 hidden space-x-8 lg:block\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                to={item.href}\n                className={`text-base font-medium transition-colors duration-200 ${\n                  isActive(item.href)\n                    ? 'text-primary-600 border-b-2 border-primary-600 pb-1'\n                    : 'text-gray-700 hover:text-primary-600'\n                }`}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <button\n              type=\"button\"\n              className=\"inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"space-y-1 px-2 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`block rounded-md px-3 py-2 text-base font-medium transition-colors duration-200 ${\n                    isActive(item.href)\n                      ? 'bg-primary-100 text-primary-700'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                  }`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,EAAEC,SAAS,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAI,CAAC,EAC9B;EAAED,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAU,CAAC,EACnC;EAAED,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAU,CAAC,EACnC;EAAED,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAU,CAAC,EACnC;EAAED,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE;AAAQ,CAAC,EAC/B;EAAED,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAU,CAAC,CACpC;AAED,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMc,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,QAAQ,GAAIC,IAAY,IAAK;IACjC,OAAOF,QAAQ,CAACG,QAAQ,KAAKD,IAAI;EACnC,CAAC;EAED,oBACEV,OAAA;IAAQY,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eACtDb,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAC,cAAW,KAAK;MAAAC,QAAA,gBACtEb,OAAA;QAAKY,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGb,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCb,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAClDb,OAAA;cAAKY,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFb,OAAA;gBAAMY,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNlB,OAAA;cAAAa,QAAA,gBACEb,OAAA;gBAAIY,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzElB,OAAA;gBAAGY,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNlB,OAAA;UAAKY,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7CZ,UAAU,CAACkB,GAAG,CAAEC,IAAI,iBACnBpB,OAAA,CAACL,IAAI;YAEHmB,EAAE,EAAEM,IAAI,CAACjB,IAAK;YACdS,SAAS,EAAE,wDACTH,QAAQ,CAACW,IAAI,CAACjB,IAAI,CAAC,GACf,qDAAqD,GACrD,sCAAsC,EACzC;YAAAU,QAAA,EAEFO,IAAI,CAAClB;UAAI,GARLkB,IAAI,CAAClB,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlB,OAAA;UAAKY,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBb,OAAA;YACEqB,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,oLAAoL;YAC9LU,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAAAO,QAAA,gBAElDb,OAAA;cAAMY,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC9CZ,cAAc,gBACbN,OAAA,CAACF,SAAS;cAACc,SAAS,EAAC,eAAe;cAAC,eAAY;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE1DlB,OAAA,CAACH,SAAS;cAACe,SAAS,EAAC,eAAe;cAAC,eAAY;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC1D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLZ,cAAc,iBACbN,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBb,OAAA;UAAKY,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCZ,UAAU,CAACkB,GAAG,CAAEC,IAAI,iBACnBpB,OAAA,CAACL,IAAI;YAEHmB,EAAE,EAAEM,IAAI,CAACjB,IAAK;YACdS,SAAS,EAAE,mFACTH,QAAQ,CAACW,IAAI,CAACjB,IAAI,CAAC,GACf,iCAAiC,GACjC,qDAAqD,EACxD;YACHmB,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAAC,KAAK,CAAE;YAAAM,QAAA,EAEvCO,IAAI,CAAClB;UAAI,GATLkB,IAAI,CAAClB,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACb,EAAA,CAlFID,MAAgB;EAAA,QAEHR,WAAW;AAAA;AAAA2B,EAAA,GAFxBnB,MAAgB;AAoFtB,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}