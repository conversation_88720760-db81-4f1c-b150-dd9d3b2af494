{"name": "is-binary-path", "version": "2.1.0", "description": "Check if a file path is a binary file", "license": "MIT", "repository": "sindresorhus/is-binary-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["binary", "extensions", "extension", "file", "path", "check", "detect", "is"], "dependencies": {"binary-extensions": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}