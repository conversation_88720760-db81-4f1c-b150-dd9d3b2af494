"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-value-parser")),r={preserve:!0};const s=/^place-(content|items|self)/,o=e=>("preserve"in Object(e)&&(r.preserve=Boolean(e.preserve)),{postcssPlugin:"postcss-place",Declaration:(e,{result:o})=>{s.test(e.prop.toLowerCase())&&((e,{result:o})=>{const a=e.prop.toLowerCase().match(s)[1];let l;try{l=t.default(e.value)}catch(t){e.warn(o,`Failed to parse value '${e.value}'. Leaving the original value intact.`)}if(void 0===l)return;let n=[];n=l.nodes.length?l.nodes.filter((e=>"word"===e.type||"function"===e.type)).map((e=>t.default.stringify(e))):[t.default.stringify(l)],e.cloneBefore({prop:`align-${a}`,value:n[0]}),e.cloneBefore({prop:`justify-${a}`,value:n[1]||n[0]}),r.preserve||e.remove()})(e,{result:o})}});o.postcss=!0,module.exports=o;
