{"ast": null, "code": "import axios from 'axios';\n// Create axios instance\nconst api = axios.create({\n  baseURL: 'http://localhost:8000/api/v1',\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// Request interceptor\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// School API\nexport const schoolApi = {\n  getAll: () => api.get('/school').then(res => res.data),\n  getById: id => api.get(`/school/${id}`).then(res => res.data)\n};\n\n// News API\nexport const newsApi = {\n  getAll: params => api.get('/news', {\n    params\n  }).then(res => res.data),\n  getBySlug: slug => api.get(`/news/${slug}`).then(res => res.data),\n  getByCategory: (category, params) => api.get(`/news/category/${category}`, {\n    params\n  }).then(res => res.data),\n  getFeatured: () => api.get('/news/featured').then(res => res.data)\n};\n\n// Gallery API\nexport const galleryApi = {\n  getAll: params => api.get('/gallery', {\n    params\n  }).then(res => res.data),\n  getById: id => api.get(`/gallery/${id}`).then(res => res.data),\n  getByCategory: (category, params) => api.get(`/gallery/category/${category}`, {\n    params\n  }).then(res => res.data),\n  getFeatured: () => api.get('/gallery/featured').then(res => res.data)\n};\n\n// Teacher API\nexport const teacherApi = {\n  getAll: params => api.get('/teachers', {\n    params\n  }).then(res => res.data),\n  getById: id => api.get(`/teachers/${id}`).then(res => res.data)\n};\n\n// Student API\nexport const studentApi = {\n  getAchievements: () => api.get('/students/achievements').then(res => res.data)\n};\n\n// Contact API\nexport const contactApi = {\n  send: data => api.post('/contact', data).then(res => res.data)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "schoolApi", "getAll", "get", "then", "res", "data", "getById", "id", "newsApi", "params", "getBySlug", "slug", "getByCategory", "category", "getFeatured", "galleryApi", "teacher<PERSON><PERSON>", "studentApi", "getAchievements", "contactApi", "send", "post"], "sources": ["C:/xampp/htdocs/sekolah/sekolah-frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { ApiResponse, PaginatedResponse, School, News, Gallery, Teacher, Student } from '../types';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: 'http://localhost:8000/api/v1',\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// School API\nexport const schoolApi = {\n  getAll: (): Promise<ApiResponse<School[]>> =>\n    api.get('/school').then(res => res.data),\n  \n  getById: (id: number): Promise<ApiResponse<School>> =>\n    api.get(`/school/${id}`).then(res => res.data),\n};\n\n// News API\nexport const newsApi = {\n  getAll: (params?: { page?: number; category?: string; featured?: boolean }): Promise<PaginatedResponse<News>> =>\n    api.get('/news', { params }).then(res => res.data),\n  \n  getBySlug: (slug: string): Promise<ApiResponse<News>> =>\n    api.get(`/news/${slug}`).then(res => res.data),\n  \n  getByCategory: (category: string, params?: { page?: number }): Promise<PaginatedResponse<News>> =>\n    api.get(`/news/category/${category}`, { params }).then(res => res.data),\n  \n  getFeatured: (): Promise<ApiResponse<News[]>> =>\n    api.get('/news/featured').then(res => res.data),\n};\n\n// Gallery API\nexport const galleryApi = {\n  getAll: (params?: { page?: number; category?: string; type?: string }): Promise<PaginatedResponse<Gallery>> =>\n    api.get('/gallery', { params }).then(res => res.data),\n  \n  getById: (id: number): Promise<ApiResponse<Gallery>> =>\n    api.get(`/gallery/${id}`).then(res => res.data),\n  \n  getByCategory: (category: string, params?: { page?: number }): Promise<PaginatedResponse<Gallery>> =>\n    api.get(`/gallery/category/${category}`, { params }).then(res => res.data),\n  \n  getFeatured: (): Promise<ApiResponse<Gallery[]>> =>\n    api.get('/gallery/featured').then(res => res.data),\n};\n\n// Teacher API\nexport const teacherApi = {\n  getAll: (params?: { page?: number }): Promise<PaginatedResponse<Teacher>> =>\n    api.get('/teachers', { params }).then(res => res.data),\n  \n  getById: (id: number): Promise<ApiResponse<Teacher>> =>\n    api.get(`/teachers/${id}`).then(res => res.data),\n};\n\n// Student API\nexport const studentApi = {\n  getAchievements: (): Promise<ApiResponse<Student[]>> =>\n    api.get('/students/achievements').then(res => res.data),\n};\n\n// Contact API\nexport const contactApi = {\n  send: (data: { name: string; email: string; phone?: string; subject: string; message: string }): Promise<ApiResponse<any>> =>\n    api.post('/contact', data).then(res => res.data),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAE,8BAA8B;EACvCC,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,SAAS,GAAG;EACvBC,MAAM,EAAEA,CAAA,KACNvB,GAAG,CAACwB,GAAG,CAAC,SAAS,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAE1CC,OAAO,EAAGC,EAAU,IAClB7B,GAAG,CAACwB,GAAG,CAAC,WAAWK,EAAE,EAAE,CAAC,CAACJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AACjD,CAAC;;AAED;AACA,OAAO,MAAMG,OAAO,GAAG;EACrBP,MAAM,EAAGQ,MAAiE,IACxE/B,GAAG,CAACwB,GAAG,CAAC,OAAO,EAAE;IAAEO;EAAO,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAEpDK,SAAS,EAAGC,IAAY,IACtBjC,GAAG,CAACwB,GAAG,CAAC,SAASS,IAAI,EAAE,CAAC,CAACR,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAEhDO,aAAa,EAAEA,CAACC,QAAgB,EAAEJ,MAA0B,KAC1D/B,GAAG,CAACwB,GAAG,CAAC,kBAAkBW,QAAQ,EAAE,EAAE;IAAEJ;EAAO,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAEzES,WAAW,EAAEA,CAAA,KACXpC,GAAG,CAACwB,GAAG,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AAClD,CAAC;;AAED;AACA,OAAO,MAAMU,UAAU,GAAG;EACxBd,MAAM,EAAGQ,MAA4D,IACnE/B,GAAG,CAACwB,GAAG,CAAC,UAAU,EAAE;IAAEO;EAAO,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAEvDC,OAAO,EAAGC,EAAU,IAClB7B,GAAG,CAACwB,GAAG,CAAC,YAAYK,EAAE,EAAE,CAAC,CAACJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAEjDO,aAAa,EAAEA,CAACC,QAAgB,EAAEJ,MAA0B,KAC1D/B,GAAG,CAACwB,GAAG,CAAC,qBAAqBW,QAAQ,EAAE,EAAE;IAAEJ;EAAO,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAE5ES,WAAW,EAAEA,CAAA,KACXpC,GAAG,CAACwB,GAAG,CAAC,mBAAmB,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AACrD,CAAC;;AAED;AACA,OAAO,MAAMW,UAAU,GAAG;EACxBf,MAAM,EAAGQ,MAA0B,IACjC/B,GAAG,CAACwB,GAAG,CAAC,WAAW,EAAE;IAAEO;EAAO,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAExDC,OAAO,EAAGC,EAAU,IAClB7B,GAAG,CAACwB,GAAG,CAAC,aAAaK,EAAE,EAAE,CAAC,CAACJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AACnD,CAAC;;AAED;AACA,OAAO,MAAMY,UAAU,GAAG;EACxBC,eAAe,EAAEA,CAAA,KACfxC,GAAG,CAACwB,GAAG,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMc,UAAU,GAAG;EACxBC,IAAI,EAAGf,IAAuF,IAC5F3B,GAAG,CAAC2C,IAAI,CAAC,UAAU,EAAEhB,IAAI,CAAC,CAACF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI;AACnD,CAAC;AAED,eAAe3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}