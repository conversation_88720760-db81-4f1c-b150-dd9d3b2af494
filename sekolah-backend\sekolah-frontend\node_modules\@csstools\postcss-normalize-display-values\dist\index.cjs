"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=e(require("postcss-value-parser")),t=new Map([["block,flow","block"],["block,flow-root","flow-root"],["inline,flow","inline"],["inline,flow-root","inline-block"],["run-in,flow","run-in"],["list-item,block,flow","list-item"],["inline,flow,list-item","inline list-item"],["block,flex","flex"],["inline,flex","inline-flex"],["block,grid","grid"],["inline,grid","inline-grid"],["inline,ruby","ruby"],["block,table","table"],["inline,table","inline-table"],["table-cell,flow","table-cell"],["table-caption,flow","table-caption"],["ruby-base,flow","ruby-base"],["ruby-text,flow","ruby-text"]]);const n=e=>{const n=!("preserve"in Object(e))||Boolean(e.preserve);return{postcssPlugin:"postcss-normalize-display-values",prepare(){const e=new Map;return{Declaration(o){if("display"!==o.prop.toLowerCase())return;const i=o.value;if(!i)return;if(e.has(i))return void(o.value!==e.get(i)&&(o.cloneBefore({value:e.get(i)}),n||o.remove()));const r=function(e){const{nodes:n}=l.default(e);if(1===n.length)return e;const o=n.filter((e=>"word"===e.type)).map((e=>e.value.toLowerCase()));if(o.length<=1)return e;return t.get(o.join(","))||e}(i);e.set(i,r),o.value!==r&&(o.cloneBefore({value:r}),n||o.remove())}}}}};n.postcss=!0,module.exports=n;
