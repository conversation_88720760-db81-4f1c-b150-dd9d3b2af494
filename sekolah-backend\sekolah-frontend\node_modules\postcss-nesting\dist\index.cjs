"use strict";var e=require("postcss-selector-parser"),n=require("@csstools/selector-specificity");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=t(e);function r(e){if(!e.nodes.length)return void e.remove();const n=e.nodes.filter((e=>"comment"===e.type));n.length===e.nodes.length&&e.replaceWith(...n)}function s(e,n){const t=n.index(e);if(t){const e=n.cloneBefore().removeAll().append(n.nodes.slice(0,t));e.raws.semicolon=!0,r(e)}n.before(e),n.raws.semicolon=!0}function l(e,n){if(n<2)throw new Error("n must be greater than 1");if(e.length<2)throw new Error("s must be greater than 1");if(Math.pow(e.length,n)>1e4)throw new Error("Too many combinations when trying to resolve a nested selector with lists, reduce the complexity of your selectors");const t=[];for(let e=0;e<n;e++)t[e]=0;const o=[];for(;;){const r=[];for(let s=n-1;s>=0;s--){let n=t[s];if(n>=e.length){if(n=0,t[s]=0,0===s)return o;t[s-1]+=1}r[s]=e[n]}o.push(r),t[t.length-1]++}}const c=o.default.pseudo({value:":is"});function i(e){if(!e||!e.nodes)return;const n=[];let t=[];for(let r=0;r<e.nodes.length;r++)if("combinator"!==e.nodes[r].type)if(o.default.isPseudoElement(e.nodes[r]))n.push(t),t=[e.nodes[r]];else{if("tag"===e.nodes[r].type&&t.find((e=>"tag"===e.type))){const n=c.clone({}),t=e.nodes[r];t.replaceWith(n),n.append(o.default.selector({nodes:[t],value:void 0}))}t.push(e.nodes[r])}else n.push(t),n.push([e.nodes[r]]),t=[];n.push(t);const r=[];for(let e=0;e<n.length;e++){const t=n[e];t.sort(((e,n)=>"selector"===e.type&&"selector"===n.type&&e.nodes.length&&n.nodes.length?a(e.nodes[0])-a(n.nodes[0]):"selector"===e.type&&e.nodes.length?a(e.nodes[0])-a(n):"selector"===n.type&&n.nodes.length?a(e)-a(n.nodes[0]):a(e)-a(n)));for(let e=0;e<t.length;e++)r.push(t[e])}for(let n=r.length-1;n>=0;n--)r[n].remove(),e.prepend(r[n])}function a(e){return o.default.isPseudoElement(e)?u.pseudoElement:u[e.type]}const u={universal:0,tag:1,pseudoElement:2,id:3,class:4,attribute:5,pseudo:6,selector:7,string:8,root:9,comment:10};function p(e,t,r){let s=[];s=function(e){const t=e.map((e=>o.default().astSync(e))).map((e=>n.selectorSpecificity(e))),r=t[0];for(let e=1;e<t.length;e++)if(r.a!==t[e].a||r.b!==t[e].b||r.c!==t[e].c)return!1;return!0}(e)||r.noIsPseudoSelector?e.map((e=>o.default().astSync(e))):[o.default().astSync(`:is(${e.join(",")})`)];const c=[];for(let e=0;e<t.length;e++){const n=t[e];let a=1,u=[],p=0;if(o.default().astSync(n).walkNesting((()=>{p++})),p>1&&s.length>1)u=l(s,p),a=u.length;else{a=s.length;for(let e=0;e<s.length;e++){u.push([]);for(let n=0;n<p;n++)u[e].push(s[e])}}for(let e=0;e<a;e++){let t=0;const s=o.default().astSync(n);s.walk((n=>{if("nesting"!==n.type)return;let s=u[e][t];t++,"root"===s.type&&1===s.nodes.length&&(s=s.nodes[0]);const l=o.default().astSync(`:is(${s.toString()})`),c=d(s.nodes[0]),a=f(s.nodes[0]),p=d(n),g=f(n);if(c&&p)return void n.replaceWith(s.clone());if((c||a)&&(p||g)){const e=n.parent;return c&&"selector"===s.type?n.replaceWith(s.clone().nodes[0]):n.replaceWith(...s.clone().nodes),void(e&&e.nodes.length>1&&i(e))}if(c){const e=n.parent;return n.replaceWith(s.clone().nodes[0]),void(e&&i(e))}if(a){const e=n.parent;return n.replaceWith(...s.clone().nodes),void(e&&i(e))}if(h(n)){const e=n.parent;return n.replaceWith(...s.clone().nodes),void(e&&i(e))}if(m(n)){const e=n.parent;return n.replaceWith(...s.clone().nodes),void(e&&i(e))}const y=n.parent;r.noIsPseudoSelector?n.replaceWith(...s.clone().nodes):n.replaceWith(...l.clone({}).nodes),y&&i(y)})),c.push(s.toString())}}return c}function d(e){return"combinator"!==e.type&&!(e.parent&&e.parent.nodes.length>1)}function f(e,n=null){if(d(e))return!1;if(!e.parent)return!1;if(!!e.parent.nodes.find((e=>"combinator"===e.type)))return!1;return!(!!e.parent.nodes.find((e=>"nesting"===e.type))&&n&&!f(n))}function h(e){if(!e.parent)return!1;if(0!==e.parent.nodes.indexOf(e))return!1;for(let n=1;n<e.parent.nodes.length;n++)if("combinator"===e.parent.nodes[n].type&&" "!==e.parent.nodes[n].value&&">"!==e.parent.nodes[n].value)return!1;return!0}function m(e){if(d(e))return!0;if(!e.parent)return!1;for(let n=0;n<e.parent.nodes.length;n++)if("nesting"!==e.parent.nodes[n].type&&(e.parent.nodes[n].prev()||e.parent.nodes[n].next())){if(e.parent.nodes[n].prev()&&"combinator"!==e.parent.nodes[n].prev().type)return!1;if(e.parent.nodes[n].next()&&"combinator"!==e.parent.nodes[n].next().type)return!1}return!0}function g(e){const n=[];let t="",o=!1,r=0,s=!1,l=!1;for(const c of e)l?l=!1:"\\"===c?l=!0:s?c===s&&(s=!1):'"'===c||"'"===c?s=c:"("===c?r+=1:")"===c?r>0&&(r-=1):0===r&&","===c&&(o=!0),o?(""!==t&&n.push(t.trim()),t="",o=!1):t+=c;return n.push(t.trim()),n}var y=["container","document","media","supports","layer"];function v(e,n){var t,o;s(e,n),e.params=(t=n.params,o=e.params,g(t).map((e=>g(o).map((n=>`${e} and ${n}`)).join(", "))).join(", ")),r(n)}function b(e){return e&&"atrule"===e.type}function w(e){return e&&"rule"===e.type}function S(e,n){e.each((e=>{const t=e.parent;w(e)&&w(t)&&function(e){return e.selectors.every((e=>0===e.trim().indexOf("&")&&-1===e.indexOf("|")))}(e)?function(e,n,t){s(e,n),e.selectors=p(n.selectors,e.selectors,t),"rule"===e.type&&"rule"===n.type&&e.selector===n.selector&&e.append(...n.nodes),r(n)}(e,t,n):function(e){return e&&b(e)&&"nest"===e.name}(e)&&w(t)&&function(e){return g(e.params).every((e=>e.split("&").length>=2&&-1===e.indexOf("|")))}(e)?function(e,n,t,o){s(e,n);const l=n.clone().removeAll().append(e.nodes);l.raws.semicolon=!0,e.replaceWith(l),l.selectors=p(n.selectors,g(e.params),o),r(n),t(l,o)}(e,t,S,n):b(e)&&w(t)&&function(e){return y.includes(e.name)}(e)?function(e,n,t,o){s(e,n);const l=n.clone().removeAll().append(e.nodes);e.append(l),r(n),t(l,o)}(e,t,S,n):b(e)&&b(t)&&function(e,n){return y.includes(e.name)&&e.name===n.name}(e,t)&&v(e,t),"nodes"in e&&e.nodes.length&&S(e,n)}))}const W=e=>{const n=Object.assign({noIsPseudoSelector:!1},e);return{postcssPlugin:"postcss-nesting",Rule(e){S(e,n)}}};W.postcss=!0,module.exports=W;
