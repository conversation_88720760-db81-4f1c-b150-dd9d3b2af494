{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\sekolah\\\\sekolah-frontend\\\\src\\\\components\\\\Layout.tsx\";\nimport React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Header from './Header';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1\",\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Outlet", "Header", "Footer", "jsxDEV", "_jsxDEV", "Layout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/sekolah/sekolah-frontend/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Header from './Header';\nimport Footer from './Footer';\n\nconst Layout: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      <main className=\"flex-1\">\n        <Outlet />\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA;IAAKE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCH,OAAA,CAACH,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAME,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACtBH,OAAA,CAACJ,MAAM;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACPP,OAAA,CAACF,MAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIP,MAAgB;AAYtB,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}